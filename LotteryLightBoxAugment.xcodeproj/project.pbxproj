// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		556F0DA92E1CFDF300E4569F /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 556F0DA82E1CFDF300E4569F /* WidgetKit.framework */; };
		556F0DAB2E1CFDF300E4569F /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 556F0DAA2E1CFDF300E4569F /* SwiftUI.framework */; };
		556F0DB82E1CFDF400E4569F /* LotteryLightBoxWidgetExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 556F0DA62E1CFDF300E4569F /* LotteryLightBoxWidgetExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		556F0D862E1CFDD200E4569F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 556F0D6D2E1CFDD000E4569F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 556F0D742E1CFDD000E4569F;
			remoteInfo = LotteryLightBoxAugment;
		};
		556F0D902E1CFDD200E4569F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 556F0D6D2E1CFDD000E4569F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 556F0D742E1CFDD000E4569F;
			remoteInfo = LotteryLightBoxAugment;
		};
		556F0DB62E1CFDF400E4569F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 556F0D6D2E1CFDD000E4569F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 556F0DA52E1CFDF300E4569F;
			remoteInfo = LotteryLightBoxWidgetExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		556F0DBD2E1CFDF400E4569F /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				556F0DB82E1CFDF400E4569F /* LotteryLightBoxWidgetExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		556F0D752E1CFDD000E4569F /* LotteryLightBoxAugment.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = LotteryLightBoxAugment.app; sourceTree = BUILT_PRODUCTS_DIR; };
		556F0D852E1CFDD200E4569F /* LotteryLightBoxAugmentTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LotteryLightBoxAugmentTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		556F0D8F2E1CFDD200E4569F /* LotteryLightBoxAugmentUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LotteryLightBoxAugmentUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		556F0DA62E1CFDF300E4569F /* LotteryLightBoxWidgetExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = LotteryLightBoxWidgetExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		556F0DA82E1CFDF300E4569F /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		556F0DAA2E1CFDF300E4569F /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		556F0DB92E1CFDF400E4569F /* Exceptions for "LotteryLightBoxWidget" folder in "LotteryLightBoxWidgetExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 556F0DA52E1CFDF300E4569F /* LotteryLightBoxWidgetExtension */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		556F0D772E1CFDD000E4569F /* LotteryLightBoxAugment */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LotteryLightBoxAugment;
			sourceTree = "<group>";
		};
		556F0D882E1CFDD200E4569F /* LotteryLightBoxAugmentTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LotteryLightBoxAugmentTests;
			sourceTree = "<group>";
		};
		556F0D922E1CFDD200E4569F /* LotteryLightBoxAugmentUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LotteryLightBoxAugmentUITests;
			sourceTree = "<group>";
		};
		556F0DAC2E1CFDF300E4569F /* LotteryLightBoxWidget */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				556F0DB92E1CFDF400E4569F /* Exceptions for "LotteryLightBoxWidget" folder in "LotteryLightBoxWidgetExtension" target */,
			);
			path = LotteryLightBoxWidget;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		556F0D722E1CFDD000E4569F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		556F0D822E1CFDD200E4569F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		556F0D8C2E1CFDD200E4569F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		556F0DA32E1CFDF300E4569F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				556F0DAB2E1CFDF300E4569F /* SwiftUI.framework in Frameworks */,
				556F0DA92E1CFDF300E4569F /* WidgetKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		556F0D6C2E1CFDD000E4569F = {
			isa = PBXGroup;
			children = (
				556F0D772E1CFDD000E4569F /* LotteryLightBoxAugment */,
				556F0D882E1CFDD200E4569F /* LotteryLightBoxAugmentTests */,
				556F0D922E1CFDD200E4569F /* LotteryLightBoxAugmentUITests */,
				556F0DAC2E1CFDF300E4569F /* LotteryLightBoxWidget */,
				556F0DA72E1CFDF300E4569F /* Frameworks */,
				556F0D762E1CFDD000E4569F /* Products */,
			);
			sourceTree = "<group>";
		};
		556F0D762E1CFDD000E4569F /* Products */ = {
			isa = PBXGroup;
			children = (
				556F0D752E1CFDD000E4569F /* LotteryLightBoxAugment.app */,
				556F0D852E1CFDD200E4569F /* LotteryLightBoxAugmentTests.xctest */,
				556F0D8F2E1CFDD200E4569F /* LotteryLightBoxAugmentUITests.xctest */,
				556F0DA62E1CFDF300E4569F /* LotteryLightBoxWidgetExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		556F0DA72E1CFDF300E4569F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				556F0DA82E1CFDF300E4569F /* WidgetKit.framework */,
				556F0DAA2E1CFDF300E4569F /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		556F0D742E1CFDD000E4569F /* LotteryLightBoxAugment */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 556F0D992E1CFDD200E4569F /* Build configuration list for PBXNativeTarget "LotteryLightBoxAugment" */;
			buildPhases = (
				556F0D712E1CFDD000E4569F /* Sources */,
				556F0D722E1CFDD000E4569F /* Frameworks */,
				556F0D732E1CFDD000E4569F /* Resources */,
				556F0DBD2E1CFDF400E4569F /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				556F0DB72E1CFDF400E4569F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				556F0D772E1CFDD000E4569F /* LotteryLightBoxAugment */,
			);
			name = LotteryLightBoxAugment;
			packageProductDependencies = (
			);
			productName = LotteryLightBoxAugment;
			productReference = 556F0D752E1CFDD000E4569F /* LotteryLightBoxAugment.app */;
			productType = "com.apple.product-type.application";
		};
		556F0D842E1CFDD200E4569F /* LotteryLightBoxAugmentTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 556F0D9C2E1CFDD200E4569F /* Build configuration list for PBXNativeTarget "LotteryLightBoxAugmentTests" */;
			buildPhases = (
				556F0D812E1CFDD200E4569F /* Sources */,
				556F0D822E1CFDD200E4569F /* Frameworks */,
				556F0D832E1CFDD200E4569F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				556F0D872E1CFDD200E4569F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				556F0D882E1CFDD200E4569F /* LotteryLightBoxAugmentTests */,
			);
			name = LotteryLightBoxAugmentTests;
			packageProductDependencies = (
			);
			productName = LotteryLightBoxAugmentTests;
			productReference = 556F0D852E1CFDD200E4569F /* LotteryLightBoxAugmentTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		556F0D8E2E1CFDD200E4569F /* LotteryLightBoxAugmentUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 556F0D9F2E1CFDD200E4569F /* Build configuration list for PBXNativeTarget "LotteryLightBoxAugmentUITests" */;
			buildPhases = (
				556F0D8B2E1CFDD200E4569F /* Sources */,
				556F0D8C2E1CFDD200E4569F /* Frameworks */,
				556F0D8D2E1CFDD200E4569F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				556F0D912E1CFDD200E4569F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				556F0D922E1CFDD200E4569F /* LotteryLightBoxAugmentUITests */,
			);
			name = LotteryLightBoxAugmentUITests;
			packageProductDependencies = (
			);
			productName = LotteryLightBoxAugmentUITests;
			productReference = 556F0D8F2E1CFDD200E4569F /* LotteryLightBoxAugmentUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		556F0DA52E1CFDF300E4569F /* LotteryLightBoxWidgetExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 556F0DBA2E1CFDF400E4569F /* Build configuration list for PBXNativeTarget "LotteryLightBoxWidgetExtension" */;
			buildPhases = (
				556F0DA22E1CFDF300E4569F /* Sources */,
				556F0DA32E1CFDF300E4569F /* Frameworks */,
				556F0DA42E1CFDF300E4569F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				556F0DAC2E1CFDF300E4569F /* LotteryLightBoxWidget */,
			);
			name = LotteryLightBoxWidgetExtension;
			packageProductDependencies = (
			);
			productName = LotteryLightBoxWidgetExtension;
			productReference = 556F0DA62E1CFDF300E4569F /* LotteryLightBoxWidgetExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		556F0D6D2E1CFDD000E4569F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					556F0D742E1CFDD000E4569F = {
						CreatedOnToolsVersion = 16.4;
					};
					556F0D842E1CFDD200E4569F = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 556F0D742E1CFDD000E4569F;
					};
					556F0D8E2E1CFDD200E4569F = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 556F0D742E1CFDD000E4569F;
					};
					556F0DA52E1CFDF300E4569F = {
						CreatedOnToolsVersion = 16.4;
					};
				};
			};
			buildConfigurationList = 556F0D702E1CFDD000E4569F /* Build configuration list for PBXProject "LotteryLightBoxAugment" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 556F0D6C2E1CFDD000E4569F;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 556F0D762E1CFDD000E4569F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				556F0D742E1CFDD000E4569F /* LotteryLightBoxAugment */,
				556F0D842E1CFDD200E4569F /* LotteryLightBoxAugmentTests */,
				556F0D8E2E1CFDD200E4569F /* LotteryLightBoxAugmentUITests */,
				556F0DA52E1CFDF300E4569F /* LotteryLightBoxWidgetExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		556F0D732E1CFDD000E4569F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		556F0D832E1CFDD200E4569F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		556F0D8D2E1CFDD200E4569F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		556F0DA42E1CFDF300E4569F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		556F0D712E1CFDD000E4569F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		556F0D812E1CFDD200E4569F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		556F0D8B2E1CFDD200E4569F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		556F0DA22E1CFDF300E4569F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		556F0D872E1CFDD200E4569F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 556F0D742E1CFDD000E4569F /* LotteryLightBoxAugment */;
			targetProxy = 556F0D862E1CFDD200E4569F /* PBXContainerItemProxy */;
		};
		556F0D912E1CFDD200E4569F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 556F0D742E1CFDD000E4569F /* LotteryLightBoxAugment */;
			targetProxy = 556F0D902E1CFDD200E4569F /* PBXContainerItemProxy */;
		};
		556F0DB72E1CFDF400E4569F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 556F0DA52E1CFDF300E4569F /* LotteryLightBoxWidgetExtension */;
			targetProxy = 556F0DB62E1CFDF400E4569F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		556F0D972E1CFDD200E4569F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		556F0D982E1CFDD200E4569F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		556F0D9A2E1CFDD200E4569F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = LotteryLightBoxAugment/LotteryLightBoxAugment.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightBoxAugment;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		556F0D9B2E1CFDD200E4569F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = LotteryLightBoxAugment/LotteryLightBoxAugment.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightBoxAugment;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		556F0D9D2E1CFDD200E4569F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightBoxAugmentTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/LotteryLightBoxAugment.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/LotteryLightBoxAugment";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		556F0D9E2E1CFDD200E4569F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightBoxAugmentTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/LotteryLightBoxAugment.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/LotteryLightBoxAugment";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		556F0DA02E1CFDD200E4569F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightBoxAugmentUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = LotteryLightBoxAugment;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		556F0DA12E1CFDD200E4569F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightBoxAugmentUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = LotteryLightBoxAugment;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		556F0DBB2E1CFDF400E4569F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = LotteryLightBoxWidget/LotteryLightBoxWidget.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LotteryLightBoxWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LotteryLightBoxWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightBoxAugment.LotteryLightBoxWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		556F0DBC2E1CFDF400E4569F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = LotteryLightBoxWidget/LotteryLightBoxWidget.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LotteryLightBoxWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LotteryLightBoxWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightBoxAugment.LotteryLightBoxWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				VALIDATE_PRODUCT = YES;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		556F0D702E1CFDD000E4569F /* Build configuration list for PBXProject "LotteryLightBoxAugment" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				556F0D972E1CFDD200E4569F /* Debug */,
				556F0D982E1CFDD200E4569F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		556F0D992E1CFDD200E4569F /* Build configuration list for PBXNativeTarget "LotteryLightBoxAugment" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				556F0D9A2E1CFDD200E4569F /* Debug */,
				556F0D9B2E1CFDD200E4569F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		556F0D9C2E1CFDD200E4569F /* Build configuration list for PBXNativeTarget "LotteryLightBoxAugmentTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				556F0D9D2E1CFDD200E4569F /* Debug */,
				556F0D9E2E1CFDD200E4569F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		556F0D9F2E1CFDD200E4569F /* Build configuration list for PBXNativeTarget "LotteryLightBoxAugmentUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				556F0DA02E1CFDD200E4569F /* Debug */,
				556F0DA12E1CFDD200E4569F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		556F0DBA2E1CFDF400E4569F /* Build configuration list for PBXNativeTarget "LotteryLightBoxWidgetExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				556F0DBB2E1CFDF400E4569F /* Debug */,
				556F0DBC2E1CFDF400E4569F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 556F0D6D2E1CFDD000E4569F /* Project object */;
}
