# LotteryLightbox - Project Specification

## Overview

LotteryLightbox is a cross-platform SwiftUI application that displays lottery information (Powerball and Mega Millions) with a distinctive digital street lightbox aesthetic. The app supports iOS, macOS, and visionOS with integrated WidgetKit support.

## Architecture

### Platform Support

- **iOS**: 18.5+ (iPhone, iPad)
- **macOS**: 15.0+
- **visionOS**: 2.5+

### Core Technologies

- **SwiftUI**: Primary UI framework
- **WidgetKit**: Widget extensions for all platforms
- **SwiftSoup**: HTML parsing for web scraping of Powerball
- **App Groups**: Data sharing between app and widgets

## Data Models

### LotteryType

```swift
enum LotteryType: String, CaseIterable {
    case powerball = "Powerball"
    case megaMillions = "Mega Millions"
}
```

### DrawingInfo

```swift
struct DrawingInfo: Decodable, Hashable {
    let jackpot: String
    let cash: String
    let drawingDate: Date
    let nextDrawingDate: Date?
    let winningNumbers: [Int]?
    let theBall: Int?
    let winners: [Winner]
}

enum WinnerType: String, Decodable {
    case jackpot = "JACKPOT WINNERS"
    case match5 = "MATCH 5 WINNERS"
}

struct Winner: Decodable {
    let state: String
    let amount: String
    let type: WinnerType
}
```

## Build Configuration

### Targets

- **LotteryLightbox**: Main app (iOS, macOS, visionOS)
- **LotteryLightboxWidgetExtension**: Widget extension (iOS, macOS, visionOS)

### Key Build Settings

```
SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator"
IPHONEOS_DEPLOYMENT_TARGET = 18.5
MACOSX_DEPLOYMENT_TARGET = 15.0
XROS_DEPLOYMENT_TARGET = 2.5
TARGETED_DEVICE_FAMILY = "1,2,6"
SDKROOT = auto
```

## User Interface

### Single View Layout

- **Main View**: Shows Powerball and Mega Millions informations in the scroll view
- **Responsive Design**: Adapts to different screen sizes and platforms

## Testing Strategy

### Unit Tests

- Service layer testing
- Data model validation
- Web scraping reliability
- Widget data management

### Test Data

- Local HTML files for scraping tests
- Mock lottery data for UI testing
- Widget preview data

## Development Guidelines

### Code Organization

- Separate views into individual Swift files
- Include SwiftUI previews for all views
- Use MVVM architecture pattern
- Platform-specific code with conditional compilation

### Widget Development

- Design for realistic digital lightbox appearance
- Support all widget sizes
- Implement proper data refresh mechanisms
- Test on all supported platforms

### Performance Considerations

- Efficient web scraping with minimal network calls
- Optimized widget refresh intervals
- Memory-efficient data sharing between app and widgets

## Future Enhancements

### Potential Features

- Additional lottery games
- Historical data tracking
- Push notifications for jackpot updates
- Customizable widget themes
- Apple Watch support

### Technical Improvements

- Enhanced error handling
- Offline data caching
- Advanced widget configurations
- Accessibility improvements

## Deployment

### App Store Requirements

- Privacy policy for web scraping
- Background app refresh permissions
- Widget configuration descriptions
- Platform-specific feature documentation

### Distribution

- iOS App Store
- Mac App Store
- TestFlight for beta testing

## API Specifications

### Web Scraping Endpoints

#### Powerball Service

- **URL**: `https://www.powerball.com/`
- **Method**: POST
- **Headers**: `Content-Type: application/json`
- **Selectors**:
  - Jackpot: `div#next-drawing` or similar ID-based selectors
  - Drawing Date: `div[data-drawdateutc]` attribute parsing
  - Next Drawing: Date parsing from drawing info sections

#### Mega Millions Service

- **URL**: `https://www.megamillions.com/`
- **Method**: POST
- **Headers**: `Content-Type: application/json`
- **Selectors**:
  - Jackpot: `div#nextDraw` or similar ID-based selectors
  - Drawing Date: `data-drawdateutc` attribute parsing
  - Next Drawing: Date parsing from drawing info sections

### Data Sharing API

#### App Groups

- **Identifier**: `group.koo.jaesung.LotteryLightbox`
- **Purpose**: Share lottery data between main app and widget extension
- **Data Format**: JSON-encoded `DrawingInfo` objects

#### Widget Timeline

```swift
struct LotteryEntry: TimelineEntry {
    let date: Date
    let powerballInfo: DrawingInfo?
    let megaMillionsInfo: DrawingInfo?
    let configuration: ConfigurationIntent
}
```

## Error Handling

### Network Errors

- Connection timeouts
- Invalid HTML structure
- Missing data elements
- Rate limiting responses

### Data Validation

- Jackpot amount parsing
- Date format validation
- Number range verification
- Fallback to cached data

### Widget Errors

- Timeline generation failures
- Data sharing issues
- Platform compatibility checks
- Graceful degradation

## Security Considerations

### Web Scraping

- Respect robots.txt guidelines
- Implement rate limiting
- Handle anti-scraping measures
- User-Agent string management

### Data Privacy

- No personal data collection
- Local data storage only
- App Group sandboxing
- Minimal network permissions

## Performance Metrics

### Target Performance

- App launch time: < 2 seconds
- Widget refresh: < 5 seconds
- Memory usage: < 50MB
- Network requests: < 1MB per update

### Optimization Strategies

- Lazy loading of views
- Efficient image caching
- Background task management
- Widget timeline optimization

## Accessibility

### VoiceOver Support

- Semantic labels for all UI elements
- Lottery number announcements
- Drawing date accessibility
- Navigation hints

### Dynamic Type

- Scalable text throughout app
- Maintain LED aesthetic at all sizes
- Responsive layout adjustments
- Contrast ratio compliance

## Localization

### Supported Languages

- English (primary)
- Extensible for additional languages

### Localizable Content

- Lottery type names
- Date formatting
- Currency formatting
- Error messages
- Widget descriptions
