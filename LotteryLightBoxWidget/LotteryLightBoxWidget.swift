//
//  LotteryLightBoxWidget.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import WidgetKit
import SwiftUI

struct LotteryTimelineProvider: TimelineProvider {
    typealias Entry = LotteryEntry

    func placeholder(in context: Context) -> LotteryEntry {
        print("Widget: placeholder called")
        return LotteryEntry.placeholder
    }

    func getSnapshot(in context: Context, completion: @escaping (LotteryEntry) -> Void) {
        print("Widget: getSnapshot called")
        let dataManager = WidgetDataManager.shared
        let (powerball, megaMillions) = dataManager.getDataForWidget()

        print("Widget: getSnapshot - Powerball: \(powerball?.jackpot ?? "nil"), MegaMillions: \(megaMillions?.jackpot ?? "nil")")

        // If no data found, use fallback test data
        let finalPowerball = powerball ?? DrawingInfo(
            jackpot: "$100 Million",
            cash: "$50 Million",
            drawingDate: Date(),
            nextDrawingDate: Calendar.current.date(byAdding: .day, value: 2, to: Date()),
            winningNumbers: [1, 2, 3, 4, 5],
            theBall: 10,
            winners: []
        )

        let finalMegaMillions = megaMillions ?? DrawingInfo(
            jackpot: "$200 Million",
            cash: "$100 Million",
            drawingDate: Date(),
            nextDrawingDate: Calendar.current.date(byAdding: .day, value: 3, to: Date()),
            winningNumbers: [6, 7, 8, 9, 10],
            theBall: 20,
            winners: []
        )

        let entry = LotteryEntry(
            date: Date(),
            powerballInfo: finalPowerball,
            megaMillionsInfo: finalMegaMillions
        )

        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<LotteryEntry>) -> Void) {
        print("Widget: getTimeline called")
        let dataManager = WidgetDataManager.shared
        let (powerball, megaMillions) = dataManager.getDataForWidget()

        print("Widget: getTimeline - Powerball: \(powerball?.jackpot ?? "nil"), MegaMillions: \(megaMillions?.jackpot ?? "nil")")

        // Debug: Test UserDefaults access directly
        let userDefaults = UserDefaults(suiteName: "group.koo.jaesung.LotteryLightbox") ?? UserDefaults.standard
        if let data = userDefaults.data(forKey: "lottery_data") {
            print("Widget: Found \(data.count) bytes in UserDefaults")
        } else {
            print("Widget: No data found in UserDefaults")
        }

        // If no data found, use fallback test data
        let finalPowerball = powerball ?? DrawingInfo(
            jackpot: "$100 Million",
            cash: "$50 Million",
            drawingDate: Date(),
            nextDrawingDate: Calendar.current.date(byAdding: .day, value: 2, to: Date()),
            winningNumbers: [1, 2, 3, 4, 5],
            theBall: 10,
            winners: []
        )

        let finalMegaMillions = megaMillions ?? DrawingInfo(
            jackpot: "$200 Million",
            cash: "$100 Million",
            drawingDate: Date(),
            nextDrawingDate: Calendar.current.date(byAdding: .day, value: 3, to: Date()),
            winningNumbers: [6, 7, 8, 9, 10],
            theBall: 20,
            winners: []
        )

        let entry = LotteryEntry(
            date: Date(),
            powerballInfo: finalPowerball,
            megaMillionsInfo: finalMegaMillions
        )

        // Refresh every 30 minutes
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 30, to: Date()) ?? Date()
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))

        completion(timeline)
    }
}

struct LotteryWidgetEntryView: View {
    var entry: LotteryEntry
    @Environment(\.widgetFamily) var family

    var body: some View {
        switch family {
        case .systemSmall:
            SmallLotteryWidget(entry: entry)
        case .systemMedium:
            MediumLotteryWidget(entry: entry)
        case .systemLarge:
            LargeLotteryWidget(entry: entry)
        default:
            SmallLotteryWidget(entry: entry)
        }
    }
}

struct LotteryLightBoxWidget: Widget {
    let kind: String = "LotteryLightBoxWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: LotteryTimelineProvider()) { entry in
            LotteryWidgetEntryView(entry: entry)
                .containerBackground(.black, for: .widget)
        }
        .configurationDisplayName("Lottery Lightbox")
        .description("View current Powerball and Mega Millions jackpots with authentic LED lightbox styling.")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
    }
}
