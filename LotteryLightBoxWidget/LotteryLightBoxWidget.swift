//
//  LotteryLightBoxWidget.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import WidgetKit
import SwiftUI

struct LotteryTimelineProvider: TimelineProvider {
    typealias Entry = LotteryEntry

    func placeholder(in context: Context) -> LotteryEntry {
        LotteryEntry.placeholder
    }

    func getSnapshot(in context: Context, completion: @escaping (LotteryEntry) -> Void) {
        let dataManager = WidgetDataManager.shared
        let (powerball, megaMillions) = dataManager.getDataForWidget()

        let entry = LotteryEntry(
            date: Date(),
            powerballInfo: powerball,
            megaMillionsInfo: megaMillions
        )

        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<LotteryEntry>) -> Void) {
        let dataManager = WidgetDataManager.shared
        let (powerball, megaMillions) = dataManager.getDataForWidget()

        let entry = LotteryEntry(
            date: Date(),
            powerballInfo: powerball,
            megaMillionsInfo: megaMillions
        )

        // Refresh every 30 minutes
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 30, to: Date()) ?? Date()
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))

        completion(timeline)
    }
}

struct LotteryWidgetEntryView: View {
    var entry: LotteryEntry
    @Environment(\.widgetFamily) var family

    var body: some View {
        switch family {
        case .systemSmall:
            SmallLotteryWidget(entry: entry)
        case .systemMedium:
            MediumLotteryWidget(entry: entry)
        case .systemLarge:
            LargeLotteryWidget(entry: entry)
        default:
            SmallLotteryWidget(entry: entry)
        }
    }
}

struct LotteryLightBoxWidget: Widget {
    let kind: String = "LotteryLightBoxWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: LotteryTimelineProvider()) { entry in
            LotteryWidgetEntryView(entry: entry)
                .containerBackground(.black, for: .widget)
        }
        .configurationDisplayName("Lottery Lightbox")
        .description("View current Powerball and Mega Millions jackpots with authentic LED lightbox styling.")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
    }
}
