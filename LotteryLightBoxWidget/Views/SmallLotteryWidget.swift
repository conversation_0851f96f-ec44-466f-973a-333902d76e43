//
//  SmallLotteryWidget.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import SwiftUI
import WidgetKit

struct SmallLotteryWidget: View {
    let entry: LotteryEntry
    @State private var showPowerball = true
    
    private var currentLottery: (type: LotteryType, info: DrawingInfo?) {
        return showPowerball ? 
            (.powerball, entry.powerballInfo) : 
            (.megaMillions, entry.megaMillionsInfo)
    }
    
    var body: some View {
        ZStack {
            // Background with glass effect
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.black)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.1),
                                    Color.clear,
                                    Color.white.opacity(0.05)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                )
            
            VStack(spacing: 4) {
                // Header with lottery indicator
                headerView
                
                Spacer()
                
                // Main jackpot display
                jackpotView
                
                Spacer()
                
                // Cash value
                cashValueView
                
                // Next draw countdown
                nextDrawView
            }
            .padding(8)
        }
        .onAppear {
            startRotationTimer()
        }
    }
    
    private var headerView: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(currentLottery.type == .powerball ? Color.red : Color.yellow)
                .frame(width: 6, height: 6)
            
            Text(currentLottery.type == .powerball ? "POWERBALL" : "MEGA MILLIONS")
                .font(.system(size: 8, weight: .bold, design: .monospaced))
                .foregroundColor(.white)
                .tracking(0.5)
                .lineLimit(1)
                .minimumScaleFactor(0.7)
            
            Spacer()
        }
    }
    
    private var jackpotView: some View {
        Group {
            if let info = currentLottery.info {
                LEDText(
                    text: abbreviateJackpot(info.jackpot),
                    color: Color(hex: "#FF334D"),
                    fontSize: 18,
                    glowIntensity: 0.8
                )
            } else {
                LEDText(
                    text: "NO DATA",
                    color: Color(hex: "#FF334D"),
                    fontSize: 14,
                    glowIntensity: 0.6
                )
            }
        }
    }
    
    private var cashValueView: some View {
        Group {
            if let info = currentLottery.info {
                LEDText(
                    text: abbreviateJackpot(info.cash),
                    color: Color(hex: "#66CCFF"),
                    fontSize: 10,
                    glowIntensity: 0.5
                )
            } else {
                EmptyView()
            }
        }
    }
    
    private var nextDrawView: some View {
        Group {
            if let info = currentLottery.info {
                VStack(spacing: 2) {
                    Text("NEXT DRAW")
                        .font(.system(size: 6, weight: .bold, design: .monospaced))
                        .foregroundColor(.gray)
                        .tracking(0.5)
                    
                    LEDText(
                        text: info.timeUntilNextDraw,
                        color: Color(hex: "#FFCC33"),
                        fontSize: 8,
                        glowIntensity: 0.4
                    )
                }
            } else {
                EmptyView()
            }
        }
    }
    
    private func abbreviateJackpot(_ amount: String) -> String {
        // Convert "$1.2 Billion" to "$1.2B", "$735 Million" to "$735M"
        return amount
            .replacingOccurrences(of: " Billion", with: "B")
            .replacingOccurrences(of: " Million", with: "M")
    }
    
    private func startRotationTimer() {
        // Switch between Powerball and Mega Millions every 10 seconds
        Timer.scheduledTimer(withTimeInterval: 10.0, repeats: true) { _ in
            withAnimation(.easeInOut(duration: 0.5)) {
                showPowerball.toggle()
            }
        }
    }
}

// MARK: - LED Text Component

struct LEDText: View {
    let text: String
    let color: Color
    let fontSize: CGFloat
    let glowIntensity: Double
    
    var body: some View {
        Text(text)
            .font(.system(size: fontSize, weight: .bold, design: .monospaced))
            .foregroundColor(color)
            .shadow(color: color.opacity(glowIntensity), radius: 2, x: 0, y: 0)
            .shadow(color: color.opacity(glowIntensity * 0.5), radius: 4, x: 0, y: 0)
            .lineLimit(1)
            .minimumScaleFactor(0.6)
    }
}

// MARK: - Color Extension

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

#Preview(as: .systemSmall) {
    LotteryLightBoxWidget()
} timeline: {
    LotteryEntry.placeholder
}
