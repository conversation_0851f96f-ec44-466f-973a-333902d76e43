//
//  LargeLotteryWidget.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import SwiftUI
import WidgetKit

struct LargeLotteryWidget: View {
    let entry: LotteryEntry
    
    var body: some View {
        VStack(spacing: 12) {
            // Header
            headerView
            
            // Lottery sections
            VStack(spacing: 16) {
                LargeLotterySection(
                    lotteryType: .powerball,
                    drawingInfo: entry.powerballInfo
                )
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                LargeLotterySection(
                    lotteryType: .megaMillions,
                    drawingInfo: entry.megaMillionsInfo
                )
            }
            
            Spacer()
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.black)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.1),
                                    Color.clear,
                                    Color.white.opacity(0.05)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                )
        )
    }
    
    private var headerView: some View {
        Text("🎰 LOTTERY LIGHTBOX")
            .font(.system(size: 12, weight: .bold, design: .monospaced))
            .foregroundColor(.white)
            .tracking(1)
    }
}

struct LargeLotterySection: View {
    let lotteryType: LotteryType
    let drawingInfo: DrawingInfo?
    
    var body: some View {
        VStack(spacing: 8) {
            // Header with lottery name and status
            HStack {
                HStack(spacing: 6) {
                    Circle()
                        .fill(lotteryType == .powerball ? Color.red : Color.yellow)
                        .frame(width: 10, height: 10)
                    
                    Text(lotteryType.displayName.uppercased())
                        .font(.system(size: 11, weight: .bold, design: .monospaced))
                        .foregroundColor(.white)
                        .tracking(1)
                }
                
                Spacer()
                
                if drawingInfo != nil {
                    Text("LIVE")
                        .font(.system(size: 8, weight: .bold, design: .monospaced))
                        .foregroundColor(.green)
                        .padding(.horizontal, 4)
                        .padding(.vertical, 2)
                        .background(Color.green.opacity(0.2))
                        .cornerRadius(3)
                } else {
                    Text("NO DATA")
                        .font(.system(size: 8, weight: .bold, design: .monospaced))
                        .foregroundColor(.red)
                        .padding(.horizontal, 4)
                        .padding(.vertical, 2)
                        .background(Color.red.opacity(0.2))
                        .cornerRadius(3)
                }
            }
            
            if let info = drawingInfo {
                // Main content
                HStack(spacing: 16) {
                    // Left side - Jackpot and cash
                    VStack(alignment: .leading, spacing: 4) {
                        LEDText(
                            text: abbreviateJackpot(info.jackpot),
                            color: Color(hex: "#FF334D"),
                            fontSize: 18,
                            glowIntensity: 0.8
                        )
                        
                        HStack(spacing: 4) {
                            Text("CASH:")
                                .font(.system(size: 7, weight: .bold, design: .monospaced))
                                .foregroundColor(.gray)
                            
                            LEDText(
                                text: abbreviateJackpot(info.cash),
                                color: Color(hex: "#66CCFF"),
                                fontSize: 10,
                                glowIntensity: 0.5
                            )
                        }
                    }
                    
                    Spacer()
                    
                    // Right side - Next draw
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("NEXT DRAW")
                            .font(.system(size: 7, weight: .bold, design: .monospaced))
                            .foregroundColor(.gray)
                            .tracking(0.5)
                        
                        LEDText(
                            text: info.timeUntilNextDraw,
                            color: Color(hex: "#FFCC33"),
                            fontSize: 10,
                            glowIntensity: 0.4
                        )
                    }
                }
                
                // Winning numbers (if available)
                if let numbers = info.winningNumbers, let ball = info.theBall {
                    winningNumbersView(numbers: numbers, specialBall: ball)
                }
                
                // Winners info (if available)
                if !info.winners.isEmpty {
                    winnersView(info.winners)
                }
                
            } else {
                // Error state
                LEDText(
                    text: "NO DATA AVAILABLE",
                    color: Color(hex: "#FF334D"),
                    fontSize: 14,
                    glowIntensity: 0.6
                )
            }
        }
    }
    
    private func winningNumbersView(numbers: [Int], specialBall: Int) -> some View {
        VStack(spacing: 4) {
            Text("WINNING NUMBERS")
                .font(.system(size: 6, weight: .bold, design: .monospaced))
                .foregroundColor(.gray)
                .tracking(0.5)
            
            HStack(spacing: 4) {
                ForEach(numbers, id: \.self) { number in
                    NumberBall(
                        number: number,
                        isSpecial: false,
                        lotteryType: lotteryType
                    )
                }
                
                Text("•")
                    .foregroundColor(.gray)
                    .font(.caption2)
                
                NumberBall(
                    number: specialBall,
                    isSpecial: true,
                    lotteryType: lotteryType
                )
            }
        }
    }
    
    private func winnersView(_ winners: [Winner]) -> some View {
        VStack(spacing: 2) {
            Text("RECENT WINNERS")
                .font(.system(size: 6, weight: .bold, design: .monospaced))
                .foregroundColor(.gray)
                .tracking(0.5)
            
            ForEach(winners.prefix(2), id: \.state) { winner in
                HStack {
                    Text(winner.state)
                        .font(.system(size: 7, weight: .medium, design: .monospaced))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Text(abbreviateAmount(winner.amount))
                        .font(.system(size: 7, weight: .bold, design: .monospaced))
                        .foregroundColor(winner.type == .jackpot ? .yellow : .green)
                }
            }
        }
    }
    
    private func abbreviateJackpot(_ amount: String) -> String {
        return amount
            .replacingOccurrences(of: " Billion", with: "B")
            .replacingOccurrences(of: " Million", with: "M")
    }
    
    private func abbreviateAmount(_ amount: String) -> String {
        return amount
            .replacingOccurrences(of: ",000,000,000", with: "B")
            .replacingOccurrences(of: ",000,000", with: "M")
            .replacingOccurrences(of: ",000", with: "K")
    }
}

struct NumberBall: View {
    let number: Int
    let isSpecial: Bool
    let lotteryType: LotteryType
    
    private var ballColor: Color {
        if isSpecial {
            return lotteryType == .powerball ? .red : .yellow
        } else {
            return .white
        }
    }
    
    private var textColor: Color {
        return isSpecial ? .white : .black
    }
    
    var body: some View {
        ZStack {
            Circle()
                .fill(ballColor)
                .frame(width: 20, height: 20)
                .shadow(color: ballColor.opacity(0.5), radius: 1, x: 0, y: 0)
            
            Text("\(number)")
                .font(.system(size: 9, weight: .bold, design: .monospaced))
                .foregroundColor(textColor)
        }
    }
}

#Preview(as: .systemLarge) {
    LotteryLightBoxWidget()
} timeline: {
    LotteryEntry.placeholder
}
