//
//  MediumLotteryWidget.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import SwiftUI
import WidgetKit

struct MediumLotteryWidget: View {
    let entry: LotteryEntry
    
    var body: some View {
        HStack(spacing: 8) {
            // Powerball section
            LotterySection(
                lotteryType: .powerball,
                drawingInfo: entry.powerballInfo
            )
            
            // Divider
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .frame(width: 1)
            
            // Mega Millions section
            LotterySection(
                lotteryType: .megaMillions,
                drawingInfo: entry.megaMillionsInfo
            )
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.black)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.1),
                                    Color.clear,
                                    Color.white.opacity(0.05)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                )
        )
    }
}

struct LotterySection: View {
    let lotteryType: LotteryType
    let drawingInfo: DrawingInfo?
    
    var body: some View {
        VStack(spacing: 6) {
            // Header
            headerView
            
            Spacer()
            
            // Jackpot
            jackpotView
            
            // Cash value
            cashValueView
            
            Spacer()
            
            // Next draw
            nextDrawView
        }
    }
    
    private var headerView: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(lotteryType == .powerball ? Color.red : Color.yellow)
                .frame(width: 8, height: 8)
            
            Text(lotteryType == .powerball ? "POWERBALL" : "MEGA MILLIONS")
                .font(.system(size: 9, weight: .bold, design: .monospaced))
                .foregroundColor(.white)
                .tracking(0.5)
                .lineLimit(1)
                .minimumScaleFactor(0.7)
            
            Spacer()
            
            // Status indicator
            if drawingInfo != nil {
                Circle()
                    .fill(Color.green)
                    .frame(width: 4, height: 4)
            } else {
                Circle()
                    .fill(Color.red)
                    .frame(width: 4, height: 4)
            }
        }
    }
    
    private var jackpotView: some View {
        Group {
            if let info = drawingInfo {
                LEDText(
                    text: abbreviateJackpot(info.jackpot),
                    color: Color(hex: "#FF334D"),
                    fontSize: 16,
                    glowIntensity: 0.8
                )
            } else {
                LEDText(
                    text: "NO DATA",
                    color: Color(hex: "#FF334D"),
                    fontSize: 12,
                    glowIntensity: 0.6
                )
            }
        }
    }
    
    private var cashValueView: some View {
        Group {
            if let info = drawingInfo {
                VStack(spacing: 2) {
                    Text("CASH")
                        .font(.system(size: 6, weight: .bold, design: .monospaced))
                        .foregroundColor(.gray)
                        .tracking(0.5)
                    
                    LEDText(
                        text: abbreviateJackpot(info.cash),
                        color: Color(hex: "#66CCFF"),
                        fontSize: 10,
                        glowIntensity: 0.5
                    )
                }
            } else {
                EmptyView()
            }
        }
    }
    
    private var nextDrawView: some View {
        Group {
            if let info = drawingInfo {
                VStack(spacing: 2) {
                    Text("NEXT DRAW")
                        .font(.system(size: 6, weight: .bold, design: .monospaced))
                        .foregroundColor(.gray)
                        .tracking(0.5)
                    
                    LEDText(
                        text: info.timeUntilNextDraw,
                        color: Color(hex: "#FFCC33"),
                        fontSize: 8,
                        glowIntensity: 0.4
                    )
                }
            } else {
                Text("NO DATA")
                    .font(.system(size: 6, weight: .bold, design: .monospaced))
                    .foregroundColor(.red)
            }
        }
    }
    
    private func abbreviateJackpot(_ amount: String) -> String {
        return amount
            .replacingOccurrences(of: " Billion", with: "B")
            .replacingOccurrences(of: " Million", with: "M")
    }
}

#Preview(as: .systemMedium) {
    LotteryLightBoxWidget()
} timeline: {
    LotteryEntry.placeholder
}
