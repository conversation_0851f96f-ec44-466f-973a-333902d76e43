//
//  SharedModels.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import Foundation
import WidgetKit

// MARK: - Lottery Types

enum LotteryType: String, CaseIterable, Codable {
    case powerball = "Powerball"
    case megaMillions = "Mega Millions"
    
    var displayName: String {
        return self.rawValue
    }
    
    var colorScheme: LotteryColorScheme {
        switch self {
        case .powerball:
            return LotteryColorScheme(primary: .red, secondary: .white, accent: .blue)
        case .megaMillions:
            return LotteryColorScheme(primary: .yellow, secondary: .blue, accent: .white)
        }
    }
}

struct LotteryColorScheme {
    let primary: LotteryColor
    let secondary: LotteryColor
    let accent: LotteryColor
}

enum LotteryColor {
    case red, blue, yellow, white, green
    
    var ledColor: String {
        switch self {
        case .red: return "#FF334D"
        case .blue: return "#66CCFF"
        case .yellow: return "#FFCC33"
        case .white: return "#FFFFFF"
        case .green: return "#33FF66"
        }
    }
}

// MARK: - Drawing Info

struct DrawingInfo: Codable, Hashable {
    let jackpot: String
    let cash: String
    let drawingDate: Date
    let nextDrawingDate: Date?
    let winningNumbers: [Int]?
    let theBall: Int?
    let winners: [Winner]
    
    // Helper computed properties for display
    var formattedJackpot: String {
        return jackpot
    }
    
    var formattedCash: String {
        return cash
    }
    
    var timeUntilNextDraw: String {
        guard let nextDate = nextDrawingDate else { return "TBD" }
        
        let now = Date()
        let timeInterval = nextDate.timeIntervalSince(now)
        
        if timeInterval <= 0 {
            return "Drawing Soon"
        }
        
        let hours = Int(timeInterval) / 3600
        let days = hours / 24
        
        if days > 0 {
            return days == 1 ? "Tomorrow" : "In \(days) days"
        } else if hours > 0 {
            return hours == 1 ? "In 1 hour" : "In \(hours) hours"
        } else {
            let minutes = Int(timeInterval) / 60
            return minutes <= 1 ? "Soon" : "In \(minutes) min"
        }
    }
}

enum WinnerType: String, Codable {
    case jackpot = "JACKPOT WINNERS"
    case match5 = "MATCH 5 WINNERS"
}

struct Winner: Codable, Hashable {
    let state: String
    let amount: String
    let type: WinnerType
}

// MARK: - Widget Timeline Entry

struct LotteryEntry: TimelineEntry {
    let date: Date
    let powerballInfo: DrawingInfo?
    let megaMillionsInfo: DrawingInfo?
    
    static var placeholder: LotteryEntry {
        LotteryEntry(
            date: Date(),
            powerballInfo: DrawingInfo.mockPowerball,
            megaMillionsInfo: DrawingInfo.mockMegaMillions
        )
    }
}

// MARK: - Shared Data

struct SharedLotteryData: Codable {
    let powerball: DrawingInfo?
    let megaMillions: DrawingInfo?
    let lastUpdated: Date
}

// MARK: - Mock Data for Previews

extension DrawingInfo {
    static var mockPowerball: DrawingInfo {
        DrawingInfo(
            jackpot: "$1.2 Billion",
            cash: "$551.7 Million",
            drawingDate: Date().addingTimeInterval(-86400), // Yesterday
            nextDrawingDate: Date().addingTimeInterval(86400), // Tomorrow
            winningNumbers: [7, 13, 23, 34, 47],
            theBall: 15,
            winners: [
                Winner(state: "CA", amount: "$1,200,000,000", type: .jackpot),
                Winner(state: "TX", amount: "$1,000,000", type: .match5),
                Winner(state: "NY", amount: "$1,000,000", type: .match5)
            ]
        )
    }
    
    static var mockMegaMillions: DrawingInfo {
        DrawingInfo(
            jackpot: "$735 Million",
            cash: "$356.7 Million",
            drawingDate: Date().addingTimeInterval(-172800), // 2 days ago
            nextDrawingDate: Date().addingTimeInterval(172800), // In 2 days
            winningNumbers: [12, 18, 33, 39, 49],
            theBall: 22,
            winners: [
                Winner(state: "FL", amount: "$735,000,000", type: .jackpot),
                Winner(state: "OH", amount: "$1,000,000", type: .match5)
            ]
        )
    }
}

// MARK: - Data Manager for Widget

class WidgetDataManager {
    static let shared = WidgetDataManager()
    private let appGroupIdentifier = "group.koo.jaesung.LotteryLightbox"
    
    private init() {}
    
    func getDataForWidget() -> (powerball: DrawingInfo?, megaMillions: DrawingInfo?) {
        // Try App Groups first
        if let sharedContainer = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier) {
            print("Widget: Shared container URL: \(sharedContainer.path)")

            // Test if we can read the test file from main app
            let testURL = sharedContainer.appendingPathComponent("test.txt")
            if FileManager.default.fileExists(atPath: testURL.path) {
                do {
                    let content = try String(contentsOf: testURL, encoding: .utf8)
                    print("Widget: ✅ Successfully read test file from main app: \(content)")
                } catch {
                    print("Widget: ❌ Failed to read test file: \(error)")
                }
            } else {
                print("Widget: ⚠️ Test file from main app does not exist")
            }

            let dataURL = sharedContainer.appendingPathComponent("lottery_data.json")
            print("Widget: Looking for data at: \(dataURL.path)")

            // Check if file exists
            if FileManager.default.fileExists(atPath: dataURL.path) {
                do {
                    let data = try Data(contentsOf: dataURL)
                    print("Widget: Successfully loaded \(data.count) bytes of data from App Groups")
                    let sharedData = try JSONDecoder().decode(SharedLotteryData.self, from: data)
                    print("Widget: Successfully decoded App Groups data - Powerball: \(sharedData.powerball?.jackpot ?? "nil"), MegaMillions: \(sharedData.megaMillions?.jackpot ?? "nil")")
                    return (sharedData.powerball, sharedData.megaMillions)
                } catch {
                    print("Widget: Failed to load cached lottery data from App Groups: \(error)")
                }
            } else {
                print("Widget: lottery_data.json does not exist in App Groups")

                // List all files in shared container for debugging
                do {
                    let files = try FileManager.default.contentsOfDirectory(atPath: sharedContainer.path)
                    print("Widget: Files in shared container: \(files)")
                } catch {
                    print("Widget: Failed to list files in shared container: \(error)")
                }
            }
        } else {
            print("Widget: Failed to get App Groups container URL")
        }

        // Try UserDefaults as fallback
        print("Widget: Trying UserDefaults fallback")
        let userDefaults = UserDefaults(suiteName: appGroupIdentifier) ?? UserDefaults.standard

        if let data = userDefaults.data(forKey: "lottery_data") {
            do {
                let sharedData = try JSONDecoder().decode(SharedLotteryData.self, from: data)
                print("Widget: Successfully loaded data from UserDefaults - Powerball: \(sharedData.powerball?.jackpot ?? "nil"), MegaMillions: \(sharedData.megaMillions?.jackpot ?? "nil")")
                return (sharedData.powerball, sharedData.megaMillions)
            } catch {
                print("Widget: Failed to decode lottery data from UserDefaults: \(error)")
            }
        } else {
            print("Widget: No data found in UserDefaults")
        }

        return (nil, nil)
    }
}
