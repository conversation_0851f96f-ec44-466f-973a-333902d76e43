//
//  ContentView.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var dataManager = LotteryDataManager.shared
    @State private var showingRefreshAnimation = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    headerView

                    // Lottery Cards
                    VStack(spacing: 16) {
                        LotteryCardView(
                            lotteryType: .powerball,
                            drawingInfo: dataManager.powerballInfo
                        )

                        LotteryCardView(
                            lotteryType: .megaMillions,
                            drawingInfo: dataManager.megaMillionsInfo
                        )
                    }
                    .padding(.horizontal)

                    // Test Buttons for debugging
                    VStack(spacing: 8) {
                        But<PERSON>("🔄 Force Refresh & Save Data") {
                            Task {
                                await dataManager.refreshData()
                            }
                        }
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)

                        But<PERSON>("🧪 Test Shared Container") {
                            dataManager.testSharedContainer()
                        }
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }

                    // Last Updated Info
                    if let lastUpdated = dataManager.lastUpdated {
                        lastUpdatedView(lastUpdated)
                    }

                    // Error Message
                    if let errorMessage = dataManager.errorMessage {
                        errorView(errorMessage)
                    }
                }
                .padding(.vertical)
            }
            .background(Color.black)
            .navigationTitle("Lottery Lightbox")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: refreshData) {
                        Image(systemName: "arrow.clockwise")
                            .foregroundColor(.white)
                            .rotationEffect(.degrees(showingRefreshAnimation ? 360 : 0))
                            .animation(
                                showingRefreshAnimation ?
                                Animation.linear(duration: 1).repeatForever(autoreverses: false) :
                                .default,
                                value: showingRefreshAnimation
                            )
                    }
                    .disabled(dataManager.isLoading)
                }
            }
        }
        .preferredColorScheme(.dark)
        .onAppear {
            // Immediately save mock data for testing
            Task {
                await dataManager.refreshData()
            }
        }
        .task {
            await dataManager.refreshData()
        }
    }

    private var headerView: some View {
        VStack(spacing: 8) {
            Text("🎰 LOTTERY LIGHTBOX")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .tracking(2)

            Text("Live Jackpot Information")
                .font(.caption)
                .foregroundColor(.gray)
                .tracking(1)
        }
        .padding()
    }

    private func lastUpdatedView(_ date: Date) -> some View {
        Text("Last updated: \(date, formatter: DateFormatter.shortTime)")
            .font(.caption2)
            .foregroundColor(.gray)
            .padding(.top, 8)
    }

    private func errorView(_ message: String) -> some View {
        HStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.red)
            Text(message)
                .font(.caption)
                .foregroundColor(.red)
        }
        .padding()
        .background(Color.red.opacity(0.1))
        .cornerRadius(8)
        .padding(.horizontal)
    }

    private func refreshData() {
        showingRefreshAnimation = true
        Task {
            await dataManager.refreshData()
            showingRefreshAnimation = false
        }
    }
}

extension DateFormatter {
    static let shortTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.dateStyle = .none
        return formatter
    }()
}
