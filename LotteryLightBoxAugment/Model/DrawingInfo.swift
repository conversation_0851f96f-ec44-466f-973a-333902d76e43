//
//  DrawingInfo.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import Foundation

struct DrawingInfo: Decodable {
    let jackpot: String
    let cash: String
    let drawingDate: Date
    let nextDrawingDate: Date
    let winningNumbers: [Int]?
    let theBall: Int?
    let winners: [Winner]
}

enum WinnerType: String, Decodable {
    case jackpot = "JACKPOT WINNERS"
    case match5 = "MATCH 5 WINNERS"
}

struct Winner: Decodable {
    let state: String
    let amount: String
    let type: WinnerType
}
