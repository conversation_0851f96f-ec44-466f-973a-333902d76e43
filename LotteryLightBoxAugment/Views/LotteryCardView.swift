//
//  LotteryCardView.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import SwiftUI

struct LotteryCardView: View {
    let lotteryType: LotteryType
    let drawingInfo: DrawingInfo?
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with lottery name and logo
            headerSection
            
            // Main content area
            contentSection
            
            // Winners section (if available)
            if let info = drawingInfo, !info.winners.isEmpty {
                winnersSection(info.winners)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
        .overlay(
            // Glass reflection effect
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.1),
                            Color.clear,
                            Color.white.opacity(0.05)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        )
    }
    
    private var headerSection: some View {
        HStack {
            // Lottery type indicator circle
            Circle()
                .fill(lotteryType == .powerball ? Color.red : Color.yellow)
                .frame(width: 12, height: 12)
            
            Text(lotteryType.displayName.uppercased())
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .tracking(1.5)
            
            Spacer()
            
            // Status indicator
            if drawingInfo != nil {
                Text("LIVE")
                    .font(.caption2)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.green.opacity(0.2))
                    .cornerRadius(4)
            } else {
                Text("NO DATA")
                    .font(.caption2)
                    .fontWeight(.bold)
                    .foregroundColor(.red)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.red.opacity(0.2))
                    .cornerRadius(4)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.gray.opacity(0.1))
    }
    
    private var contentSection: some View {
        VStack(spacing: 16) {
            if let info = drawingInfo {
                // Jackpot amount - main focus
                LEDTextView(
                    text: info.formattedJackpot,
                    color: Color(hex: "#FF334D"), // Bright red LED
                    fontSize: 32,
                    glowIntensity: 0.8
                )
                
                // Cash value
                HStack {
                    Text("CASH VALUE:")
                        .font(.caption)
                        .foregroundColor(.gray)
                        .tracking(1)
                    
                    Spacer()
                    
                    LEDTextView(
                        text: info.formattedCash,
                        color: Color(hex: "#66CCFF"), // Blue LED
                        fontSize: 16,
                        glowIntensity: 0.5
                    )
                }
                
                // Next drawing info
                HStack {
                    Text("NEXT DRAW:")
                        .font(.caption)
                        .foregroundColor(.gray)
                        .tracking(1)
                    
                    Spacer()
                    
                    LEDTextView(
                        text: info.timeUntilNextDraw,
                        color: Color(hex: "#FFCC33"), // Yellow LED
                        fontSize: 14,
                        glowIntensity: 0.4
                    )
                }
                
                // Winning numbers (if available)
                if let numbers = info.winningNumbers, let ball = info.theBall {
                    winningNumbersView(numbers: numbers, specialBall: ball)
                }
                
            } else {
                // Error state
                LEDTextView(
                    text: "NO DATA",
                    color: Color(hex: "#FF334D"),
                    fontSize: 24,
                    glowIntensity: 0.6
                )
                
                Text("Unable to load lottery information")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(16)
    }
    
    private func winningNumbersView(numbers: [Int], specialBall: Int) -> some View {
        VStack(spacing: 8) {
            Text("WINNING NUMBERS")
                .font(.caption2)
                .foregroundColor(.gray)
                .tracking(1)
            
            HStack(spacing: 8) {
                ForEach(numbers, id: \.self) { number in
                    NumberBallView(
                        number: number,
                        isSpecial: false,
                        lotteryType: lotteryType
                    )
                }
                
                // Special ball separator
                Text("•")
                    .foregroundColor(.gray)
                    .font(.title2)
                
                NumberBallView(
                    number: specialBall,
                    isSpecial: true,
                    lotteryType: lotteryType
                )
            }
        }
    }
    
    private func winnersSection(_ winners: [Winner]) -> some View {
        VStack(spacing: 8) {
            Divider()
                .background(Color.gray.opacity(0.3))
            
            Text("RECENT WINNERS")
                .font(.caption2)
                .foregroundColor(.gray)
                .tracking(1)
            
            ForEach(winners.prefix(3), id: \.state) { winner in
                HStack {
                    Text(winner.state)
                        .font(.caption)
                        .foregroundColor(.white)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text(winner.amount)
                        .font(.caption)
                        .foregroundColor(winner.type == .jackpot ? .yellow : .green)
                        .fontWeight(.bold)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 12)
    }
}

// MARK: - Supporting Views

struct LEDTextView: View {
    let text: String
    let color: Color
    let fontSize: CGFloat
    let glowIntensity: Double
    
    var body: some View {
        Text(text)
            .font(.system(size: fontSize, weight: .bold, design: .monospaced))
            .foregroundColor(color)
            .shadow(color: color.opacity(glowIntensity), radius: 4, x: 0, y: 0)
            .shadow(color: color.opacity(glowIntensity * 0.5), radius: 8, x: 0, y: 0)
    }
}

struct NumberBallView: View {
    let number: Int
    let isSpecial: Bool
    let lotteryType: LotteryType
    
    private var ballColor: Color {
        if isSpecial {
            return lotteryType == .powerball ? .red : .yellow
        } else {
            return .white
        }
    }
    
    private var textColor: Color {
        return isSpecial ? .white : .black
    }
    
    var body: some View {
        ZStack {
            Circle()
                .fill(ballColor)
                .frame(width: 32, height: 32)
                .shadow(color: ballColor.opacity(0.5), radius: 2, x: 0, y: 0)
            
            Text("\(number)")
                .font(.system(size: 14, weight: .bold, design: .monospaced))
                .foregroundColor(textColor)
        }
    }
}

// MARK: - Color Extension

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

#Preview {
    VStack(spacing: 20) {
        LotteryCardView(
            lotteryType: .powerball,
            drawingInfo: DrawingInfo.mockPowerball
        )
        
        LotteryCardView(
            lotteryType: .megaMillions,
            drawingInfo: DrawingInfo.mockMegaMillions
        )
    }
    .padding()
    .background(Color.black)
    .preferredColorScheme(.dark)
}
