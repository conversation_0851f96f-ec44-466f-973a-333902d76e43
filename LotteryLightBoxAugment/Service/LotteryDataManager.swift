//
//  LotteryDataManager.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import Foundation
import Combine
import WidgetKit

@MainActor
class LotteryDataManager: ObservableObject {
    static let shared = LotteryDataManager()
    
    @Published var powerballInfo: DrawingInfo?
    @Published var megaMillionsInfo: DrawingInfo?
    @Published var isLoading = false
    @Published var lastUpdated: Date?
    @Published var errorMessage: String?
    
    private let powerballService: LotterServiceProtocol
    private let megaMillionsService: LotterServiceProtocol
    private let appGroupIdentifier = "group.koo.jaesung.LotteryLightbox"
    
    private init() {
        self.powerballService = PowerballService()
        self.megaMillionsService = MegaMillionsService()
        loadCachedData()
    }
    
    // MARK: - Public Methods
    
    func refreshData() async {
        print("Main App: refreshData() called")
        isLoading = true
        errorMessage = nil

        do {
            print("Main App: Starting to fetch lottery data...")
            // Fetch both lottery data concurrently
            async let powerballData = fetchPowerballData()
            async let megaMillionsData = fetchMegaMillionsData()

            let (powerball, megaMillions) = try await (powerballData, megaMillionsData)

            print("Main App: Successfully fetched data - Powerball: \(powerball.jackpot), MegaMillions: \(megaMillions.jackpot)")

            self.powerballInfo = powerball
            self.megaMillionsInfo = megaMillions
            self.lastUpdated = Date()

            // Save to shared container for widgets
            print("Main App: About to save to shared container...")
            saveToSharedContainer()

            // Trigger widget refresh
            print("Main App: Triggering widget refresh...")
            WidgetCenter.shared.reloadAllTimelines()

        } catch {
            self.errorMessage = "Failed to fetch lottery data: \(error.localizedDescription)"
            print("Main App: Error fetching lottery data: \(error)")
        }

        isLoading = false
        print("Main App: refreshData() completed")
    }
    
    func getDataForWidget() -> (powerball: DrawingInfo?, megaMillions: DrawingInfo?) {
        loadCachedData()
        return (powerballInfo, megaMillionsInfo)
    }

    func testSharedContainer() {
        print("Main App: Testing shared container access...")
        guard let sharedContainer = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier) else {
            print("Main App: ❌ Failed to get shared container URL")
            return
        }

        print("Main App: ✅ Shared container URL: \(sharedContainer.path)")

        // Test writing a simple file
        let testURL = sharedContainer.appendingPathComponent("test.txt")
        do {
            try "Hello from main app".write(to: testURL, atomically: true, encoding: .utf8)
            print("Main App: ✅ Successfully wrote test file")

            let content = try String(contentsOf: testURL)
            print("Main App: ✅ Successfully read test file: \(content)")
        } catch {
            print("Main App: ❌ Failed to write/read test file: \(error)")
        }
    }
    
    // MARK: - Private Methods
    
    private func fetchPowerballData() async throws -> DrawingInfo {
        return try await powerballService.fetchNextDrawingInfo()
    }
    
    private func fetchMegaMillionsData() async throws -> DrawingInfo {
        return try await megaMillionsService.fetchNextDrawingInfo()
    }
    
    private func saveToSharedContainer() {
        // Try App Groups first
        if let sharedContainer = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier) {
            let dataURL = sharedContainer.appendingPathComponent("lottery_data.json")
            print("Main App: Saving data to App Groups: \(dataURL.path)")

            let sharedData = SharedLotteryData(
                powerball: powerballInfo,
                megaMillions: megaMillionsInfo,
                lastUpdated: lastUpdated ?? Date()
            )

            do {
                let data = try JSONEncoder().encode(sharedData)
                try data.write(to: dataURL)
                print("Main App: Successfully saved \(data.count) bytes to App Groups container")
                print("Main App: Powerball: \(powerballInfo?.jackpot ?? "nil"), MegaMillions: \(megaMillionsInfo?.jackpot ?? "nil")")
            } catch {
                print("Main App: Failed to save lottery data to App Groups container: \(error)")
            }
        } else {
            print("Main App: Failed to get App Groups container URL")
        }

        // Also save to UserDefaults as fallback
        let sharedData = SharedLotteryData(
            powerball: powerballInfo,
            megaMillions: megaMillionsInfo,
            lastUpdated: lastUpdated ?? Date()
        )

        do {
            let data = try JSONEncoder().encode(sharedData)
            let userDefaults = UserDefaults(suiteName: appGroupIdentifier) ?? UserDefaults.standard
            userDefaults.set(data, forKey: "lottery_data")
            userDefaults.synchronize()
            print("Main App: Successfully saved data to UserDefaults as fallback")
        } catch {
            print("Main App: Failed to save lottery data to UserDefaults: \(error)")
        }
    }
    
    private func loadCachedData() {
        guard let sharedContainer = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier) else {
            print("Failed to get shared container URL for loading")
            return
        }
        
        let dataURL = sharedContainer.appendingPathComponent("lottery_data.json")
        
        do {
            let data = try Data(contentsOf: dataURL)
            let sharedData = try JSONDecoder().decode(SharedLotteryData.self, from: data)
            
            self.powerballInfo = sharedData.powerball
            self.megaMillionsInfo = sharedData.megaMillions
            self.lastUpdated = sharedData.lastUpdated
            
        } catch {
            print("Failed to load cached lottery data: \(error)")
        }
    }
}


