//
//  LotteryDataManager.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import Foundation
import Combine

@MainActor
class LotteryDataManager: ObservableObject {
    static let shared = LotteryDataManager()
    
    @Published var powerballInfo: DrawingInfo?
    @Published var megaMillionsInfo: DrawingInfo?
    @Published var isLoading = false
    @Published var lastUpdated: Date?
    @Published var errorMessage: String?
    
    private let powerballService: LotterServiceProtocol
    private let megaMillionsService: LotterServiceProtocol
    private let appGroupIdentifier = "group.koo.jaesung.LotteryLightbox"
    
    private init() {
        self.powerballService = PowerballService()
        self.megaMillionsService = MegaMillionsService()
        loadCachedData()
    }
    
    // MARK: - Public Methods
    
    func refreshData() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Fetch both lottery data concurrently
            async let powerballData = fetchPowerballData()
            async let megaMillionsData = fetchMegaMillionsData()
            
            let (powerball, megaMillions) = try await (powerballData, megaMillionsData)
            
            self.powerballInfo = powerball
            self.megaMillionsInfo = megaMillions
            self.lastUpdated = Date()
            
            // Save to shared container for widgets
            saveToSharedContainer()
            
        } catch {
            self.errorMessage = "Failed to fetch lottery data: \(error.localizedDescription)"
            print("Error fetching lottery data: \(error)")
        }
        
        isLoading = false
    }
    
    func getDataForWidget() -> (powerball: DrawingInfo?, megaMillions: DrawingInfo?) {
        loadCachedData()
        return (powerballInfo, megaMillionsInfo)
    }
    
    // MARK: - Private Methods
    
    private func fetchPowerballData() async throws -> DrawingInfo {
        return try await powerballService.fetchNextDrawingInfo()
    }
    
    private func fetchMegaMillionsData() async throws -> DrawingInfo {
        return try await megaMillionsService.fetchNextDrawingInfo()
    }
    
    private func saveToSharedContainer() {
        guard let sharedContainer = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier) else {
            print("Failed to get shared container URL")
            return
        }
        
        let dataURL = sharedContainer.appendingPathComponent("lottery_data.json")
        
        let sharedData = SharedLotteryData(
            powerball: powerballInfo,
            megaMillions: megaMillionsInfo,
            lastUpdated: lastUpdated ?? Date()
        )
        
        do {
            let data = try JSONEncoder().encode(sharedData)
            try data.write(to: dataURL)
            print("Successfully saved lottery data to shared container")
        } catch {
            print("Failed to save lottery data to shared container: \(error)")
        }
    }
    
    private func loadCachedData() {
        guard let sharedContainer = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier) else {
            print("Failed to get shared container URL for loading")
            return
        }
        
        let dataURL = sharedContainer.appendingPathComponent("lottery_data.json")
        
        do {
            let data = try Data(contentsOf: dataURL)
            let sharedData = try JSONDecoder().decode(SharedLotteryData.self, from: data)
            
            self.powerballInfo = sharedData.powerball
            self.megaMillionsInfo = sharedData.megaMillions
            self.lastUpdated = sharedData.lastUpdated
            
        } catch {
            print("Failed to load cached lottery data: \(error)")
        }
    }
}


