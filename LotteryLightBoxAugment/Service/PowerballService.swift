//
//  PowerballService.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import Foundation

class PowerballService: LotterServiceProtocol {
    var urlString = "https://www.powerball.com"
    var response: String?
    
    init(response: String? = nil) {
        self.response = response
    }
    
    func fetchNextDrawingInfo() async throws -> DrawingInfo {
        if response == nil {
            guard let url = URL(string: urlString) else {
                throw LotterServiceError.invalidURL
            }

            let (data, _) = try await URLSession.shared.data(from: url)
            response = String(data: data, encoding: .utf8)
        }
        
        guard let html = response else {
            throw LotterServiceError.noData
        }
        
        // Extract jackpot amount
        guard let jackpotRange = html.range(of: "Estimated Jackpot[\\s\\S]*?\\$[0-9,.]+ (Million|Billion)", options: .regularExpression),
              let jackpotText = html[jackpotRange].range(of: "\\$[0-9,.]+ (Million|Billion)", options: .regularExpression) else {
            throw LotterServiceError.parsingError
        }
        let jackpot = String(html[jackpotText])
        
        // Extract cash value
        guard let cashRange = html.range(of: "Cash Value[\\s\\S]*?\\$[0-9,.]+ (Million|Billion)", options: .regularExpression),
              let cashText = html[cashRange].range(of: "\\$[0-9,.]+ (Million|Billion)", options: .regularExpression) else {
            throw LotterServiceError.parsingError
        }
        let cashValue = String(html[cashText])
        
        // Extract winning numbers
        let winningNumbers = extractWinningNumbers(from: html)
        
        // Extract powerball number
        let theBall = extractPowerballNumber(from: html)
        
        // Extract drawing date
        let drawingDate = extractDrawingDate(from: html)
        
        // Extract next drawing date
        let nextDrawingDate = extractNextDrawingDate(from: html)
        
        // Extract winners
        let winners = extractWinners(from: html)
        
        return DrawingInfo(
            jackpot: jackpot,
            cash: cashValue,
            drawingDate: drawingDate,
            nextDrawingDate: nextDrawingDate,
            winningNumbers: winningNumbers,
            theBall: theBall,
            winners: winners
        )
    }
    
    private func extractWinningNumbers(from html: String) -> [Int]? {
        var numbers: [Int] = []
        
        // Look for white-balls pattern with more specific matching
        let whiteBallPattern = "white-balls[^>]*>(\\d+)</div>"
        let regex = try? NSRegularExpression(pattern: whiteBallPattern, options: [])
        let matches = regex?.matches(in: html, options: [], range: NSRange(location: 0, length: html.count)) ?? []
        
        for match in matches {
            if let range = Range(match.range(at: 1), in: html) {
                if let number = Int(String(html[range])) {
                    numbers.append(number)
                }
            }
        }
        
        return numbers.isEmpty ? nil : numbers
    }
    
    private func extractPowerballNumber(from html: String) -> Int? {
        // Look for the powerball number specifically - it should have "powerball" class but not "white-balls"
        let powerballPattern = "class=\"[^\"]*powerball[^\"]*\"[^>]*>(\\d+)</div>"
        let regex = try? NSRegularExpression(pattern: powerballPattern, options: [])
        let matches = regex?.matches(in: html, options: [], range: NSRange(location: 0, length: html.count)) ?? []
        
        for match in matches {
            let fullMatch = String(html[Range(match.range, in: html)!])
            // Ensure this is the powerball (has "powerball" class) and not a white ball
            if fullMatch.contains("powerball") && !fullMatch.contains("white-balls") {
                if let range = Range(match.range(at: 1), in: html) {
                    return Int(String(html[range]))
                }
            }
        }
        
        return nil
    }
    
    private func extractDrawingDate(from html: String) -> Date {
        // Look for drawing date in the winning numbers section
        if let dateRange = html.range(of: "Winning Numbers[\\s\\S]*?title-date[^>]*>([^<]+)</h5>", options: .regularExpression) {
            let dateText = String(html[dateRange])
            if let titleRange = dateText.range(of: "title-date[^>]*>([^<]+)</h5>", options: .regularExpression) {
                let dateString = String(dateText[titleRange])
                    .replacingOccurrences(of: "title-date[^>]*>", with: "", options: .regularExpression)
                    .replacingOccurrences(of: "</h5>", with: "")
                    .trimmingCharacters(in: .whitespacesAndNewlines)
                
                return parseDisplayDate(from: dateString)
            }
        }
        
        return Date()
    }
    
    private func extractNextDrawingDate(from html: String) -> Date {
        // Look for data-drawdateutc attribute
        if let utcRange = html.range(of: "data-drawdateutc=\"([^\"]+)\"", options: .regularExpression) {
            let utcText = String(html[utcRange])
            if let quotedRange = utcText.range(of: "\"([^\"]+)\"", options: .regularExpression) {
                let dateString = String(utcText[quotedRange]).replacingOccurrences(of: "\"", with: "")
                return parseUTCDate(from: dateString)
            }
        }
        
        return Date().addingTimeInterval(86400 * 3) // Default to 3 days from now
    }
    
    private func parseDisplayDate(from dateString: String) -> Date {
        // Parse format like "Mon, Jun 30, 2025"
        let formatter = DateFormatter()
        formatter.dateFormat = "EEE, MMM d, yyyy"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        
        return formatter.date(from: dateString) ?? Date()
    }
    
    private func parseUTCDate(from dateString: String) -> Date {
        // Parse format like "2025-07-03T02:59:00.0000000Z"
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        
        if let date = formatter.date(from: dateString) {
            return date
        }
        
        // Try without fractional seconds
        formatter.formatOptions = [.withInternetDateTime]
        return formatter.date(from: dateString) ?? Date()
    }
    
    private func extractWinners(from html: String) -> [Winner] {
        var winners: [Winner] = []
        
        // Look for jackpot winners
        if let jackpotRange = html.range(of: "JACKPOT WINNERS[\\s\\S]*?winner-location[^>]*>([^<]+)</span>", options: .regularExpression) {
            let jackpotText = String(html[jackpotRange])
            if let locationRange = jackpotText.range(of: "winner-location[^>]*>([^<]+)</span>", options: .regularExpression) {
                let locationText = String(jackpotText[locationRange])
                    .replacingOccurrences(of: "winner-location[^>]*>", with: "", options: .regularExpression)
                    .replacingOccurrences(of: "</span>", with: "")
                    .trimmingCharacters(in: .whitespacesAndNewlines)
                
                if !locationText.lowercased().contains("none") {
                    let jackpotWinners = parseWinnerLocations(locationText, amount: "Jackpot", type: .jackpot)
                    winners.append(contentsOf: jackpotWinners)
                }
            }
        }
        
        // Look for Match 5 + Power Play winners ($2 Million)
        if let match5PowerRange = html.range(of: "\\$2 Million Winners[\\s\\S]*?winner-location[^>]*>([^<]+)</span>", options: .regularExpression) {
            let match5PowerText = String(html[match5PowerRange])
            if let locationRange = match5PowerText.range(of: "winner-location[^>]*>([^<]+)</span>", options: .regularExpression) {
                let locationText = String(match5PowerText[locationRange])
                    .replacingOccurrences(of: "winner-location[^>]*>", with: "", options: .regularExpression)
                    .replacingOccurrences(of: "</span>", with: "")
                    .trimmingCharacters(in: .whitespacesAndNewlines)
                
                if !locationText.lowercased().contains("none") {
                    let match5PowerWinners = parseWinnerLocations(locationText, amount: "$2 Million", type: .match5)
                    winners.append(contentsOf: match5PowerWinners)
                }
            }
        }
        
        // Look for Match 5 winners ($1 Million)
        if let match5Range = html.range(of: "\\$1 Million Winners[\\s\\S]*?winner-location[^>]*>([^<]+)</span>", options: .regularExpression) {
            let match5Text = String(html[match5Range])
            if let locationRange = match5Text.range(of: "winner-location[^>]*>([^<]+)</span>", options: .regularExpression) {
                let locationText = String(match5Text[locationRange])
                    .replacingOccurrences(of: "winner-location[^>]*>", with: "", options: .regularExpression)
                    .replacingOccurrences(of: "</span>", with: "")
                    .trimmingCharacters(in: .whitespacesAndNewlines)
                
                if !locationText.lowercased().contains("none") {
                    let match5Winners = parseWinnerLocations(locationText, amount: "$1 Million", type: .match5)
                    winners.append(contentsOf: match5Winners)
                }
            }
        }
        
        return winners
    }
    
    private func parseWinnerLocations(_ locationText: String, amount: String, type: WinnerType) -> [Winner] {
        var winners: [Winner] = []
        
        // Split by comma for multiple states
        let stateEntries = locationText.components(separatedBy: ",")
        
        for stateEntry in stateEntries {
            let trimmedEntry = stateEntry.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // Check if there's a count in parentheses like "CA (2)"
            if let countRange = trimmedEntry.range(of: "\\s*\\((\\d+)\\)\\s*$", options: .regularExpression) {
                let state = String(trimmedEntry[..<countRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
                let countText = String(trimmedEntry[countRange])
                    .replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
                
                if let count = Int(countText) {
                    // Add multiple winners for the same state
                    for _ in 0..<count {
                        winners.append(Winner(state: state, amount: amount, type: type))
                    }
                }
            } else {
                // Single winner from this state
                winners.append(Winner(state: trimmedEntry, amount: amount, type: type))
            }
        }
        
        return winners
    }
}

